"use client";

import { useState } from "react";
import { Product } from "@/utils/types";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Heart, ShoppingCart, Star, Minus, Plus, Truck, Shield, RotateCcw, Check, Share2 } from "lucide-react";
import { calculateDiscountPercentage, formatPrice, isProductOnSale, getEffectivePrice } from "@/lib/product-utils";
import { useCart } from "@/contexts/cart-context";

interface ProductInfoProps {
  product: Product;
}

export default function ProductInfo({ product }: ProductInfoProps) {
  const [selectedSize, setSelectedSize] = useState<string>("");
  const [selectedColor, setSelectedColor] = useState<string>("");
  const [quantity, setQuantity] = useState(1);
  const [isFavorited, setIsFavorited] = useState(false);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [addedToCart, setAddedToCart] = useState(false);

  const { addItem, getItemQuantity } = useCart();

  const isOnSale = isProductOnSale(product);
  const effectivePrice = getEffectivePrice(product);
  const discountPercentage = isOnSale 
    ? calculateDiscountPercentage(product.price, product.discountedPrice!)
    : 0;

  const canAddToCart = product.stock > 0 &&
    (product.sizes.length === 0 || selectedSize) &&
    (product.colors.length === 0 || selectedColor);

  // Get current quantity in cart for this specific variant
  const currentCartQuantity = getItemQuantity(product.id, selectedSize, selectedColor);

  const handleAddToCart = async () => {
    if (!canAddToCart || isAddingToCart) return;

    setIsAddingToCart(true);

    try {
      addItem(product, quantity, selectedSize, selectedColor);

      // Show success feedback
      setAddedToCart(true);
      setTimeout(() => setAddedToCart(false), 2000);

      // Reset quantity to 1 after adding
      setQuantity(1);
    } catch (error) {
      console.error("Error adding to cart:", error);
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleToggleFavorite = () => {
    setIsFavorited(!isFavorited);
    // TODO: Implement favorites functionality
  };

  const handleWhatsAppShare = () => {
    // Construct the product URL
    const productUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'https://rivvsneakers.shop'}/products/${product.id}`;

    // Get the main product image
    const productImage = product.images && product.images.length > 0 ? product.images[0] : '';

    // Create a clean description (remove extra whitespace and limit length)
    const cleanDescription = product.description
      ? product.description.replace(/\s+/g, ' ').trim().substring(0, 120)
      : '';

    // Create the share message with better formatting
    const message = `🔥 *Check out this amazing product from RIVV Sneakers!*

👟 *${product.name}*
🏷️ Brand: ${product.brand}
💰 Price: ${formatPrice(effectivePrice)}${isOnSale ? ` ~~${formatPrice(product.price)}~~ *SALE!*` : ''}
⭐ Rating: ${product.rating.toFixed(1)}/5 (${product.reviewCount} reviews)
📦 Stock: ${product.stock > 0 ? 'In Stock' : 'Out of Stock'}

${cleanDescription ? `📝 ${cleanDescription}${product.description && product.description.length > 120 ? '...' : ''}

` : ''}🛒 *Shop now:* ${productUrl}

Visit RIVV Sneakers for quality footwear and amazing deals! 🚀

#RivvSneakers #QualityFootwear #OnlineShopping #Lesotho`;

    // Encode the message for WhatsApp
    const encodedMessage = encodeURIComponent(message);

    // Open WhatsApp with the message
    const whatsappUrl = `https://wa.me/?text=${encodedMessage}`;
    window.open(whatsappUrl, '_blank');
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Star key="half" className="h-5 w-5 fill-yellow-400/50 text-yellow-400" />
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Star key={`empty-${i}`} className="h-5 w-5 text-gray-300" />
      );
    }

    return stars;
  };

  return (
    <div className="space-y-6">
      {/* Brand and Name */}
      <div>
        <p className="text-sm text-gray-500 font-medium mb-1">{product.brand}</p>
        <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
      </div>

      {/* Rating */}
      <div className="flex items-center gap-3">
        <div className="flex items-center">
          {renderStars(product.rating)}
        </div>
        <span className="text-lg font-medium">{product.rating.toFixed(1)}</span>
        <span className="text-gray-500">({product.reviewCount} reviews)</span>
      </div>

      {/* Price */}
      <div className="flex items-center gap-3">
        <span className="text-3xl font-bold text-gray-900">
          {formatPrice(effectivePrice)}
        </span>
        {isOnSale && (
          <>
            <span className="text-xl text-gray-500 line-through">
              {formatPrice(product.price)}
            </span>
            <Badge variant="destructive">
              -{discountPercentage}% OFF
            </Badge>
          </>
        )}
      </div>

      <Separator />

      {/* Description */}
      {product.description && (
        <div>
          <h3 className="font-semibold mb-2">Description</h3>
          <p className="text-gray-600 leading-relaxed">{product.description}</p>
        </div>
      )}

      {/* Color Selection */}
      {product.colors.length > 0 && (
        <div>
          <h3 className="font-semibold mb-3">Color</h3>
          <div className="flex flex-wrap gap-2">
            {product.colors.map((color) => (
              <Button
                key={color}
                variant={selectedColor === color ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedColor(color)}
                className="capitalize"
              >
                {color}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Size Selection */}
      {product.sizes.length > 0 && (
        <div>
          <h3 className="font-semibold mb-3">Size</h3>
          <div className="grid grid-cols-4 gap-2">
            {product.sizes.map((size) => (
              <Button
                key={size}
                variant={selectedSize === size ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedSize(size)}
              >
                {size}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Quantity */}
      <div>
        <h3 className="font-semibold mb-3">Quantity</h3>
        <div className="flex items-center gap-3">
          <div className="flex items-center border rounded-lg">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setQuantity(Math.max(1, quantity - 1))}
              disabled={quantity <= 1}
            >
              <Minus className="h-4 w-4" />
            </Button>
            <span className="px-4 py-2 font-medium">{quantity}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setQuantity(Math.min(product.stock, quantity + 1))}
              disabled={quantity >= product.stock}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <span className="text-sm text-gray-500">
            {product.stock} available
          </span>
        </div>
      </div>

      {/* Stock Status */}
      <div className="flex items-center gap-2">
        <div className={`h-2 w-2 rounded-full ${product.stock > 0 ? "bg-green-500" : "bg-red-500"}`} />
        <span className={`text-sm font-medium ${product.stock > 0 ? "text-green-600" : "text-red-600"}`}>
          {product.stock > 0 ? "In Stock" : "Out of Stock"}
        </span>
      </div>

      {/* Current Cart Quantity */}
      {currentCartQuantity > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <p className="text-sm text-blue-800">
            <strong>{currentCartQuantity}</strong> {currentCartQuantity === 1 ? "item" : "items"} of this variant already in cart
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        <Button
          size="lg"
          className="w-full"
          onClick={handleAddToCart}
          disabled={!canAddToCart || isAddingToCart}
        >
          {isAddingToCart ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Adding to Cart...
            </>
          ) : addedToCart ? (
            <>
              <Check className="h-5 w-5 mr-2" />
              Added to Cart!
            </>
          ) : (
            <>
              <ShoppingCart className="h-5 w-5 mr-2" />
              Add to Cart
            </>
          )}
        </Button>
        
        <Button
          variant="outline"
          size="lg"
          className="w-full"
          onClick={handleToggleFavorite}
        >
          <Heart className={`h-5 w-5 mr-2 ${isFavorited ? "fill-red-500 text-red-500" : ""}`} />
          {isFavorited ? "Remove from Favorites" : "Add to Favorites"}
        </Button>

        <Button
          variant="outline"
          size="lg"
          className="w-full bg-green-50 hover:bg-green-100 border-green-300 text-green-700 hover:text-green-800 transition-all duration-200 hover:shadow-md"
          onClick={handleWhatsAppShare}
        >
          <Share2 className="h-5 w-5 mr-2" />
          Share on WhatsApp
        </Button>
      </div>

      {/* Selection Requirements */}
      {!canAddToCart && product.stock > 0 && (
        <div className="text-sm text-amber-600 bg-amber-50 p-3 rounded-lg">
          Please select:
          {product.sizes.length > 0 && !selectedSize && " size"}
          {product.colors.length > 0 && !selectedColor && " color"}
        </div>
      )}

      <Separator />

      {/* Features */}
      <div className="space-y-3">
        <div className="flex items-center gap-3 text-sm text-gray-600">
          <Truck className="h-5 w-5" />
           Free delivery in Maseru and for orders over M3500 in other disctricts, otherwise delivery fee applies
        </div>
        
      </div>
    </div>
  );
}
